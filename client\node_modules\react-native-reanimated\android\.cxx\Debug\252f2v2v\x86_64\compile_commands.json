[{"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\5902132a011a07009ebc4c1979b47fca\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\5902132a011a07009ebc4c1979b47fca\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\8381e4f2241b07a247a8edec969abc0e\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\c373fb0cb0205ac8156540e3132b2685\\WorkletRuntime\\ReanimatedHermesRuntime.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\5902132a011a07009ebc4c1979b47fca\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\c373fb0cb0205ac8156540e3132b2685\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\5902132a011a07009ebc4c1979b47fca\\reanimated\\Fabric\\ReanimatedCommitHook.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\5902132a011a07009ebc4c1979b47fca\\reanimated\\Fabric\\ReanimatedMountHook.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\5384bd35cebe0339a8534868eb1525ec\\LayoutAnimationsManager.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\NativeModules\\NativeReanimatedModule.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\NativeReanimatedModule.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\NativeReanimatedModule.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\de26f8cead47227ca676f03d1d571aaf\\NativeReanimatedModuleSpec.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\NativeReanimatedModuleSpec.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\NativeReanimatedModuleSpec.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\RuntimeDecorators\\RNRuntimeDecorator.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\0ece905a2c2bf93f9c78f535a359f32b\\ReanimatedWorkletRuntimeDecorator.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\ReanimatedWorkletRuntimeDecorator.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\ReanimatedWorkletRuntimeDecorator.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\903cdc38e100b3e8f688e82b49cafa56\\RuntimeDecorators\\UIRuntimeDecorator.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\7519ac08560a20b167be15fe9fbea7e4\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\AndroidUIScheduler.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\AndroidUIScheduler.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\AndroidUIScheduler.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\JNIHelper.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\LayoutAnimations.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\NativeProxy.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\OnLoad.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/../Common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/src/main/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/callinvoker -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/runtimeexecutor -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/yoga -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/8efc2e640de319a2aa5b8b521a15efc3/transformed/hermes-android-0.77.0-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -DREANIMATED_VERSION=3.16.7 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\PlatformLogger.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\PlatformLogger.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\PlatformLogger.cpp"}]