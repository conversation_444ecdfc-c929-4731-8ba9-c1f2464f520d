{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": "."}, {"build": "src/main/cpp/worklets", "jsonFile": "directory-src.main.cpp.worklets-Debug-32561fb4aa5ad8ee5603.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 0, "source": "src/main/cpp/worklets", "targetIndexes": [1]}, {"build": "src/main/cpp/reanimated", "jsonFile": "directory-src.main.cpp.reanimated-Debug-d8da37d7e442c9fefebb.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 0, "source": "src/main/cpp/reanimated", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "Reanimated", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 2, "id": "reanimated::@89a6a9b85fb42923616c", "jsonFile": "target-reanimated-Debug-06e879f75964f3f3fa74.json", "name": "reanimated", "projectIndex": 0}, {"directoryIndex": 1, "id": "worklets::@a0394df2d94e5212d8bd", "jsonFile": "target-worklets-Debug-0dce141d4781458a4ec7.json", "name": "worklets", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/x86_64", "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android"}, "version": {"major": 2, "minor": 3}}