@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86_64" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86_64" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\252f2v2v\\obj\\x86_64" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\252f2v2v\\obj\\x86_64" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\252f2v2v\\prefab\\x86_64\\prefab" ^
  "-BD:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\252f2v2v\\x86_64" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_MINOR_VERSION=77" ^
  "-DANDROID_TOOLCHAIN=clang" ^
  "-DREACT_NATIVE_DIR=D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native" ^
  "-DJS_RUNTIME=hermes" ^
  "-DJS_RUNTIME_DIR=D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native\\sdks\\hermes" ^
  "-DIS_NEW_ARCHITECTURE_ENABLED=true" ^
  "-DIS_REANIMATED_EXAMPLE_APP=false" ^
  "-DREANIMATED_VERSION=3.16.7" ^
  "-DHERMES_ENABLE_DEBUGGER=1"
